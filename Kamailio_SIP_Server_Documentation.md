# Kamailio SIP Server Comprehensive Documentation Guide

## Table of Contents
1. [Introduction and Overview](#1-introduction-and-overview)
2. [Basic Configuration and Setup](#2-basic-configuration-and-setup)
3. [Core Functionality](#3-core-functionality)
4. [Advanced Features](#4-advanced-features)
5. [Practical Examples](#5-practical-examples)
6. [Troubleshooting and Maintenance](#6-troubleshooting-and-maintenance)
7. [Best Practices](#7-best-practices)

---

## 1. Introduction and Overview

### 1.1 What is Kamailio?

Kamailio (formerly OpenSER) is a high-performance, open-source SIP (Session Initiation Protocol) server that provides:

- **SIP Proxy Server**: Routes SIP messages between endpoints
- **SIP Registrar**: Manages user location and registration
- **SIP Redirect Server**: Provides alternative contact information
- **Application Server**: Hosts SIP-based applications and services

### 1.2 Primary Use Cases

**Telecommunications Infrastructure:**
- VoIP service providers
- Enterprise PBX systems
- Call centers and contact centers
- Unified communications platforms

**Real-time Communications:**
- Video conferencing systems
- Instant messaging platforms
- Presence and availability services
- WebRTC gateways

**Service Provider Solutions:**
- Carrier-grade SIP routing
- Load balancing and failover
- Session border controllers (SBC)
- Billing and accounting integration

### 1.3 Key Features and Capabilities

**Core SIP Features:**
- Full SIP RFC compliance (RFC 3261 and extensions)
- Support for UDP, TCP, TLS, SCTP, and WebSocket transports
- IPv4 and IPv6 dual-stack support
- NAT traversal capabilities
- Flexible routing engine

**Scalability and Performance:**
- Multi-process architecture
- Asynchronous processing
- Memory-efficient design
- High concurrent call capacity (thousands of calls per second)
- Horizontal scaling support

**Integration Capabilities:**
- Database backends (PostgreSQL, MySQL, MongoDB, Redis)
- LDAP and RADIUS authentication
- REST API integration
- Message queuing systems
- External application interfaces

### 1.4 Architecture Overview

**Core Components:**

```
┌─────────────────────────────────────────────────────────┐
│                    Kamailio Core                        │
├─────────────────────────────────────────────────────────┤
│  SIP Parser  │  Transaction Manager  │  Dialog Manager  │
├─────────────────────────────────────────────────────────┤
│  Routing Engine  │  Module Interface  │  Memory Manager │
├─────────────────────────────────────────────────────────┤
│  Network Layer (UDP/TCP/TLS/WS)  │  Timer Management   │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                     Modules                             │
├─────────────────────────────────────────────────────────┤
│  Database  │  Authentication  │  Accounting  │  Presence│
│  Modules   │  Modules         │  Modules     │  Modules │
├─────────────────────────────────────────────────────────┤
│  NAT       │  Load Balancer   │  Text Utils  │  JSON    │
│  Modules   │  Modules         │  Modules     │  Modules │
└─────────────────────────────────────────────────────────┘
```

**Process Architecture:**
- **Main Process**: Configuration loading and process management
- **Worker Processes**: Handle SIP message processing
- **Timer Processes**: Manage time-based operations
- **TCP Processes**: Handle TCP connections (when enabled)

**Memory Management:**
- **Private Memory**: Per-process memory allocation
- **Shared Memory**: Inter-process communication and data sharing
- **Package Memory**: Dynamic memory allocation for operations

---

## 2. Basic Configuration and Setup

### 2.1 Configuration File Structure (kamailio.cfg)

The main configuration file `/etc/kamailio/kamailio.cfg` follows a structured format:

**File Structure Overview:**
```
# Global Parameters
#!define DBURL "****************************"
listen=udp:0.0.0.0:5060

# Module Loading
loadmodule "tm.so"
loadmodule "sl.so"
loadmodule "rr.so"

# Module Parameters
modparam("tm", "fr_timer", 30000)
modparam("rr", "enable_full_lr", 1)

# Routing Logic
request_route {
    # Main request routing block
}

route[RELAY] {
    # Custom routing block
}
```

### 2.2 Essential Configuration Parameters

**Global Parameters:**
```bash
# Process and memory settings
children=8                    # Number of worker processes
tcp_children=8               # Number of TCP worker processes
shm_mem=64                   # Shared memory size (MB)
pkg_mem=8                    # Package memory size (MB)

# Network settings
listen=udp:0.0.0.0:5060      # UDP listening interface
listen=tcp:0.0.0.0:5060      # TCP listening interface
port=5060                    # Default SIP port
mhomed=1                     # Multi-homed support

# DNS and domain settings
alias="sip.example.com"      # Server alias
domain="example.com"         # Default domain

# Database connection
#!define DBURL "postgres://kamailio:password@localhost/kamailio"
```

**Security Parameters:**
```bash
# Rate limiting
pike_limit=20                # Requests per second limit
pike_time_unit=1            # Time unit for rate limiting

# Authentication
auth_checks_register=1       # Enable registration authentication
auth_checks_no_dlg=1        # Authentication for non-dialog requests

# TLS settings (when using TLS)
enable_tls=yes
tls_port_no=5061
tls_method=TLSv1.2
```

### 2.3 Basic Routing Logic Examples

**Simple Proxy Configuration:**
```bash
request_route {
    # Initial request processing
    if (!mf_process_maxfwd_header("10")) {
        sl_send_reply("483", "Too Many Hops");
        exit;
    }

    # Handle retransmissions
    if (has_totag()) {
        if (loose_route()) {
            route(RELAY);
        } else {
            if (is_method("ACK")) {
                if (t_check_trans()) {
                    t_relay();
                }
                exit;
            }
            sl_send_reply("404", "Not Found");
        }
        exit;
    }

    # Record routing for dialog-forming requests
    if (is_method("INVITE|SUBSCRIBE")) {
        record_route();
    }

    # Handle registrations
    if (is_method("REGISTER")) {
        route(REGISTRAR);
        exit;
    }

    # Route to destination
    route(LOCATION);
}

route[REGISTRAR] {
    if (!auth_check("$fd", "subscriber", "1")) {
        auth_challenge("$fd", "0");
        exit;
    }
    
    if (!save("location")) {
        sl_reply_error();
    }
    exit;
}

route[LOCATION] {
    if (!lookup("location")) {
        sl_send_reply("404", "User Not Found");
        exit;
    }
    route(RELAY);
}

route[RELAY] {
    if (!t_relay()) {
        sl_reply_error();
    }
    exit;
}
```

---

## 3. Core Functionality

### 3.1 SIP Message Handling and Routing

**Message Flow Processing:**

1. **Message Reception**: Kamailio receives SIP messages on configured interfaces
2. **Parsing**: SIP message is parsed and validated
3. **Route Processing**: Message enters the request_route block
4. **Module Processing**: Various modules process the message
5. **Forwarding**: Message is forwarded to destination or response sent

**Routing Blocks:**
```bash
# Main routing block - entry point for all requests
request_route {
    # Pre-processing logic
}

# Reply routing - handles SIP responses
onreply_route {
    # Response processing logic
}

# Failure routing - handles failed transactions
failure_route {
    # Failure handling logic
}

# Branch routing - per-branch processing
branch_route {
    # Branch-specific logic
}
```

**Message Routing Functions:**
```bash
# Forward message to destination
t_relay();

# Send stateless reply
sl_send_reply("404", "Not Found");

# Rewrite Request-URI
rewriteuri("sip:<EMAIL>");

# Add/remove headers
append_hf("X-Custom-Header: value\r\n");
remove_hf("User-Agent");
```

### 3.3 Call Processing Workflows

**Basic Call Flow:**

```
Caller (A)          Kamailio Proxy          Callee (B)
    |                      |                      |
    |------- INVITE ------>|                      |
    |                      |------- INVITE ------>|
    |                      |<------ 180 Ringing --|
    |<----- 180 Ringing ---|                      |
    |                      |<------ 200 OK -------|
    |<----- 200 OK --------|                      |
    |------- ACK --------->|                      |
    |                      |------- ACK --------->|
    |                      |                      |
    |<====== RTP Media Stream ==================>|
    |                      |                      |
    |------- BYE --------->|                      |
    |                      |------- BYE --------->|
    |                      |<------ 200 OK -------|
    |<----- 200 OK --------|                      |
```

**Call Processing Configuration:**
```bash
route[INVITE] {
    # Pre-call processing
    if (!has_totag()) {
        # New call setup
        if (is_method("INVITE")) {
            # Record route for proper dialog handling
            record_route();

            # Apply call policies
            route(CALL_POLICIES);

            # Perform authentication if required
            if (!auth_check("$fd", "subscriber", "1")) {
                auth_challenge("$fd", "0");
                exit;
            }

            # Check call permissions
            if (!allow_trusted()) {
                sl_send_reply("403", "Forbidden");
                exit;
            }

            # Route to destination
            route(LOCATION);
        }
    }
}

route[CALL_POLICIES] {
    # Time-based routing
    if (!check_time_period("business_hours")) {
        sl_send_reply("486", "Service Unavailable - Outside Business Hours");
        exit;
    }

    # Rate limiting per user
    if (!pike_check_req()) {
        sl_send_reply("503", "Service Unavailable - Rate Limited");
        exit;
    }

    # Call duration limits
    if (is_method("INVITE")) {
        append_hf("P-Call-Limit: 3600\r\n");
    }
}
```

### 3.4 Load Balancing and Failover

**Load Balancing Modules:**
- **dispatcher**: Distribute calls across multiple destinations
- **load_balancer**: Advanced load balancing with weight and capacity
- **drouting**: Dynamic routing based on rules

**Dispatcher Configuration:**
```bash
# Load dispatcher module
loadmodule "dispatcher.so"
modparam("dispatcher", "db_url", DBURL)
modparam("dispatcher", "table_name", "dispatcher")
modparam("dispatcher", "flags", 2)
modparam("dispatcher", "dst_avp", "$avp(dsdst)")
modparam("dispatcher", "grp_avp", "$avp(dsgrp)")
modparam("dispatcher", "cnt_avp", "$avp(dscnt)")

route[LOADBALANCE] {
    # Select destination from group 1
    if (!ds_select_dst("1", "4")) {
        sl_send_reply("503", "Service Unavailable");
        exit;
    }

    # Set failure route for failover
    t_on_failure("FAILOVER");

    # Forward to selected destination
    route(RELAY);
}

failure_route[FAILOVER] {
    # Check if we can try next destination
    if (t_check_status("(408)|(5[0-9][0-9])")) {
        if (ds_next_dst()) {
            # Try next destination
            t_relay();
            exit;
        }
    }

    # All destinations failed
    sl_send_reply("503", "All Gateways Unavailable");
}
```

**Database Schema for Dispatcher:**
```sql
CREATE TABLE dispatcher (
    id SERIAL PRIMARY KEY,
    setid INTEGER NOT NULL,
    destination VARCHAR(192) NOT NULL,
    flags INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 0,
    attrs VARCHAR(128),
    description VARCHAR(64)
);

-- Example entries
INSERT INTO dispatcher (setid, destination, flags, priority, description) VALUES
(1, 'sip:gw1.example.com:5060', 0, 1, 'Primary Gateway'),
(1, 'sip:gw2.example.com:5060', 0, 2, 'Secondary Gateway'),
(1, 'sip:gw3.example.com:5060', 0, 3, 'Backup Gateway');
```

---

## 4. Advanced Features

### 4.1 Database Integration

**Supported Database Backends:**
- **PostgreSQL**: Recommended for production environments
- **MySQL/MariaDB**: Widely used alternative
- **MongoDB**: NoSQL document database
- **Redis**: In-memory data structure store
- **SQLite**: Lightweight file-based database

**Database Module Configuration:**
```bash
# PostgreSQL configuration
loadmodule "db_postgres.so"
modparam("usrloc", "db_url", DBURL)
modparam("usrloc", "db_mode", 2)
modparam("usrloc", "use_domain", 1)

# Authentication database
modparam("auth_db", "db_url", DBURL)
modparam("auth_db", "calculate_ha1", yes)
modparam("auth_db", "password_column", "password")
modparam("auth_db", "load_credentials", "")

# Accounting database
loadmodule "acc.so"
modparam("acc", "db_url", DBURL)
modparam("acc", "db_flag", 1)
modparam("acc", "db_missed_flag", 2)
modparam("acc", "report_cancels", 1)
```

**Database Connection Pooling:**
```bash
# Connection pooling parameters
modparam("db_postgres", "max_db_connections", 10)
modparam("db_postgres", "db_connection_timeout", 300)
modparam("db_postgres", "ping_interval", 60)
```

### 4.2 Real-time Communication Modules

**Presence and SIMPLE:**
```bash
# Presence modules
loadmodule "presence.so"
loadmodule "presence_xml.so"
loadmodule "presence_mwi.so"
loadmodule "pua.so"

# Presence configuration
modparam("presence", "db_url", DBURL)
modparam("presence", "server_address", "sip:presence.example.com:5060")
modparam("presence", "expires_offset", 10)
modparam("presence", "max_expires", 3600)

# Handle SUBSCRIBE requests
route[PRESENCE] {
    if (is_method("SUBSCRIBE")) {
        if (!auth_check("$fd", "subscriber", "1")) {
            auth_challenge("$fd", "0");
            exit;
        }

        handle_subscribe();
        exit;
    }

    if (is_method("PUBLISH")) {
        if (!auth_check("$fd", "subscriber", "1")) {
            auth_challenge("$fd", "0");
            exit;
        }

        handle_publish();
        exit;
    }
}
```

**WebRTC Support:**
```bash
# WebRTC modules
loadmodule "websocket.so"
loadmodule "nathelper.so"
loadmodule "rtpengine.so"

# WebSocket configuration
modparam("websocket", "ping_interval", 30)
modparam("websocket", "keepalive_timeout", 10)

# RTP Engine for media handling
modparam("rtpengine", "rtpengine_sock", "udp:127.0.0.1:2223")

# WebRTC handling
route[WEBRTC] {
    if (nat_uac_test("19")) {
        if (is_method("REGISTER")) {
            fix_nated_register();
        } else {
            if (is_first_hop()) {
                set_contact_alias();
            }
        }
        setflag(NAT);
    }

    if (is_method("INVITE")) {
        if (has_body("application/sdp")) {
            rtpengine_offer();
        }
    }
}

onreply_route[WEBRTC_REPLY] {
    if (has_body("application/sdp")) {
        rtpengine_answer();
    }
}
```

### 4.3 Security Configurations

**TLS/SSL Configuration:**
```bash
# Enable TLS
enable_tls=yes
listen=tls:0.0.0.0:5061

# TLS module
loadmodule "tls.so"
modparam("tls", "config", "/etc/kamailio/tls.cfg")
modparam("tls", "tls_method", "TLSv1.2")
modparam("tls", "certificate", "/etc/kamailio/cert.pem")
modparam("tls", "private_key", "/etc/kamailio/privkey.pem")
modparam("tls", "ca_list", "/etc/kamailio/ca.pem")
```

**Anti-Fraud Protection:**
```bash
# Pike module for rate limiting
loadmodule "pike.so"
modparam("pike", "sampling_time_unit", 2)
modparam("pike", "reqs_density_per_unit", 16)
modparam("pike", "remove_latency", 4)

# Permissions module
loadmodule "permissions.so"
modparam("permissions", "db_url", DBURL)
modparam("permissions", "db_mode", 1)

route[ANTIFRAUD] {
    # Rate limiting
    if (!pike_check_req()) {
        xlog("L_ALERT", "PIKE blocking $rm from $si\n");
        sl_send_reply("503", "Service Unavailable");
        exit;
    }

    # IP-based permissions
    if (!allow_source_address()) {
        sl_send_reply("403", "Forbidden");
        exit;
    }

    # Geographic restrictions
    if (geoip_match("$si", "src")) {
        if ($gip(src=>cc) == "XX") {  # Block specific countries
            sl_send_reply("403", "Geographic Restriction");
            exit;
        }
    }
}
```

### 4.4 Performance Tuning

**Process and Memory Optimization:**
```bash
# Process configuration
children=16                  # Increase for high load
tcp_children=8              # TCP worker processes
tcp_connection_lifetime=3605 # TCP connection timeout

# Memory settings
shm_mem=128                 # Shared memory (MB)
pkg_mem=16                  # Package memory per process (MB)
real_time=1                 # Real-time priority
mlock_pages=yes             # Lock pages in memory

# Network optimization
tcp_accept_aliases=yes      # Accept TCP aliases
tcp_connection_match=1      # Connection matching
udp4_raw=1                  # Raw UDP sockets
```

**Database Optimization:**
```bash
# Database connection optimization
modparam("usrloc", "db_mode", 3)        # Write-back mode
modparam("usrloc", "timer_interval", 60) # Sync interval
modparam("usrloc", "db_update_as_insert", 1)

# Query optimization
modparam("sqlops", "sqlcon", "ca=>****************************")
modparam("sqlops", "connect_timeout", 5)
modparam("sqlops", "query_timeout", 10)
```

**Monitoring and Statistics:**
```bash
# Statistics module
loadmodule "statistics.so"
modparam("statistics", "variable", "active_calls")
modparam("statistics", "variable", "processed_requests")

# MI (Management Interface)
loadmodule "mi_fifo.so"
modparam("mi_fifo", "fifo_name", "/tmp/kamailio_fifo")

# JSONRPC-S module for API
loadmodule "jsonrpcs.so"
modparam("jsonrpcs", "pretty_format", 1)
modparam("jsonrpcs", "transport", 7)
```

---

## 5. Practical Examples

### 5.1 Common Configuration Scenarios

**Scenario 1: Basic SIP Proxy with Authentication**
```bash
#!KAMAILIO
# Basic SIP proxy with user authentication

####### Global Parameters #########
debug=2
log_stderror=no
log_facility=LOG_LOCAL0
fork=yes
children=4

listen=udp:0.0.0.0:5060
alias="sip.example.com"

####### Modules Section ########
loadmodule "tm.so"
loadmodule "sl.so"
loadmodule "rr.so"
loadmodule "pv.so"
loadmodule "maxfwd.so"
loadmodule "usrloc.so"
loadmodule "registrar.so"
loadmodule "textops.so"
loadmodule "siputils.so"
loadmodule "xlog.so"
loadmodule "sanity.so"
loadmodule "ctl.so"
loadmodule "cfg_rpc.so"
loadmodule "mi_rpc.so"
loadmodule "auth.so"
loadmodule "auth_db.so"
loadmodule "db_postgres.so"

####### Module Parameters #########
# Database URL
#!define DBURL "postgres://kamailio:password@localhost/kamailio"

# Authentication
modparam("auth_db", "db_url", DBURL)
modparam("auth_db", "calculate_ha1", yes)
modparam("auth_db", "password_column", "password")

# User location
modparam("usrloc", "db_url", DBURL)
modparam("usrloc", "db_mode", 2)

####### Routing Logic ########
request_route {
    # Per request initial checks
    route(REQINIT);

    # Handle retransmissions
    if(has_totag()) {
        if(loose_route()) {
            route(DLGURI);
            if (is_method("BYE")) {
                setflag(FLT_ACC);
            }
            route(RELAY);
        } else {
            if (is_method("ACK")) {
                if (t_check_trans()) {
                    route(RELAY);
                    exit;
                }
                exit;
            }
            sl_send_reply("404","Not here");
        }
        exit;
    }

    # Initial sanity checks
    route(SANITY);

    # Handle requests within SIP dialogs
    route(WITHINDLG);

    # Handle registrations
    route(REGISTRAR);

    # User authentication
    route(AUTH);

    # Caller NAT detection
    route(NATDETECT);

    # Handle presence related requests
    route(PRESENCE);

    # Handle location service
    route(LOCATION);
}

route[REQINIT] {
    if (!mf_process_maxfwd_header("10")) {
        sl_send_reply("483","Too Many Hops");
        exit;
    }

    if(!sanity_check("1511", "7")) {
        xlog("Malformed SIP message from $si:$sp\n");
        exit;
    }
}

route[REGISTRAR] {
    if (is_method("REGISTER")) {
        if (!auth_check("$fd", "subscriber", "1")) {
            auth_challenge("$fd", "0");
            exit;
        }

        if (!save("location")) {
            sl_reply_error();
        }
        exit;
    }
}

route[AUTH] {
    if (is_method("INVITE|SUBSCRIBE")) {
        if (!auth_check("$fd", "subscriber", "1")) {
            auth_challenge("$fd", "0");
            exit;
        }
    }
}

route[LOCATION] {
    if (!lookup("location")) {
        $var(rc) = $rc;
        t_newtran();
        switch ($var(rc)) {
            case -1:
            case -3:
                send_reply("404", "Not Found");
                exit;
            case -2:
                send_reply("405", "Method Not Allowed");
                exit;
        }
    }
    route(RELAY);
}

route[RELAY] {
    if (is_method("INVITE")) {
        t_on_branch("MANAGE_BRANCH");
        t_on_reply("MANAGE_REPLY");
        t_on_failure("MANAGE_FAILURE");
    }

    if (!t_relay()) {
        sl_reply_error();
    }
    exit;
}
```

**Scenario 2: Multi-tenant SIP Platform**
```bash
# Multi-tenant configuration with domain-based routing

####### Domain Configuration #########
loadmodule "domain.so"
modparam("domain", "db_url", DBURL)
modparam("domain", "domain_table", "domain")

# Use domain in user location
modparam("usrloc", "use_domain", 1)
modparam("auth_db", "use_domain", 1)

route[MULTITENANT] {
    # Check if domain is served locally
    if (!is_domain_local("$fd")) {
        sl_send_reply("403", "Domain not served");
        exit;
    }

    # Domain-specific processing
    if ($fd == "tenant1.example.com") {
        route(TENANT1_PROCESSING);
    } else if ($fd == "tenant2.example.com") {
        route(TENANT2_PROCESSING);
    } else {
        route(DEFAULT_PROCESSING);
    }
}

route[TENANT1_PROCESSING] {
    # Tenant 1 specific logic
    append_hf("X-Tenant: tenant1\r\n");
    # Apply tenant-specific policies
}

route[TENANT2_PROCESSING] {
    # Tenant 2 specific logic
    append_hf("X-Tenant: tenant2\r\n");
    # Apply different policies
}
```

### 5.2 Sample Routing Scripts

**Advanced Call Routing with Time-based Rules:**
```bash
route[TIME_ROUTING] {
    # Get current time
    $var(hour) = $time(hour);
    $var(wday) = $time(wday);

    # Business hours: Monday-Friday 9-17
    if ($var(wday) >= 1 && $var(wday) <= 5 &&
        $var(hour) >= 9 && $var(hour) <= 17) {

        # Route to business hours destination
        $ru = "sip:" + $rU + "@business.example.com";
        route(RELAY);

    } else {
        # Route to after-hours destination
        $ru = "sip:" + $rU + "@afterhours.example.com";
        route(RELAY);
    }
}
```

**Geographic Routing:**
```bash
loadmodule "geoip.so"
modparam("geoip", "path", "/usr/share/GeoIP/GeoIP.dat")

route[GEO_ROUTING] {
    # Get caller's country
    if (geoip_match("$si", "src")) {
        $var(country) = $gip(src=>cc);

        switch($var(country)) {
            case "US":
                $ru = "sip:" + $rU + "@us-gateway.example.com";
                break;
            case "GB":
                $ru = "sip:" + $rU + "@uk-gateway.example.com";
                break;
            case "DE":
                $ru = "sip:" + $rU + "@de-gateway.example.com";
                break;
            default:
                $ru = "sip:" + $rU + "@international.example.com";
        }

        route(RELAY);
    } else {
        sl_send_reply("403", "Geographic routing failed");
        exit;
    }
}
```

### 5.3 Integration with Other Systems

**REST API Integration:**
```bash
loadmodule "http_client.so"
loadmodule "jansson.so"

route[API_INTEGRATION] {
    # Prepare API request
    $var(api_url) = "https://api.example.com/validate";
    $var(post_data) = '{"caller":"' + $fU + '","callee":"' + $rU + '"}';

    # Make HTTP request
    if (http_client_query("$var(api_url)", "$var(post_data)", "$var(response)")) {
        # Parse JSON response
        jansson_get("allowed", "$var(response)", "$var(allowed)");

        if ($var(allowed) == "true") {
            route(RELAY);
        } else {
            sl_send_reply("403", "Call not allowed");
            exit;
        }
    } else {
        # API call failed, apply default policy
        xlog("L_WARN", "API call failed, using default policy\n");
        route(RELAY);
    }
}
```

**Database-driven Routing:**
```bash
loadmodule "sqlops.so"
modparam("sqlops", "sqlcon", "cb=>****************************")

route[DB_ROUTING] {
    # Query routing table
    sql_query("cb",
        "SELECT gateway FROM routing WHERE prefix='$var(prefix)' ORDER BY priority LIMIT 1",
        "ra");

    if ($dbr(ra=>rows) > 0) {
        $var(gateway) = $dbr(ra=>[0,0]);
        $ru = "sip:" + $rU + "@" + $var(gateway);
        sql_result_free("ra");
        route(RELAY);
    } else {
        sql_result_free("ra");
        sl_send_reply("404", "No route found");
        exit;
    }
}
```

---

## 6. Troubleshooting and Maintenance

### 6.1 Common Issues and Solutions

**Issue 1: High Memory Usage**

*Symptoms:*
- Kamailio processes consuming excessive memory
- System becoming unresponsive
- Out of memory errors in logs

*Diagnosis:*
```bash
# Check memory usage per process
ps aux | grep kamailio | awk '{print $6}' | sort -n

# Monitor shared memory usage
kamctl stats | grep shmem

# Check for memory leaks
kamctl stats | grep -E "(pkg|shm)_used"
```

*Solutions:*
```bash
# Increase package memory
pkg_mem=32

# Increase shared memory
shm_mem=256

# Enable memory debugging
memdbg=5
memlog=5

# Restart Kamailio to clear memory
systemctl restart kamailio
```

**Issue 2: Database Connection Problems**

*Symptoms:*
- "Database connection failed" errors
- Registration failures
- Intermittent service disruptions

*Diagnosis:*
```bash
# Test database connectivity
PGPASSWORD=password psql -h localhost -U kamailio -d kamailio -c "SELECT 1;"

# Check database connections
kamctl db show

# Monitor database logs
tail -f /var/log/postgresql/postgresql-*.log
```

*Solutions:*
```bash
# Increase connection pool
modparam("db_postgres", "max_db_connections", 20)

# Add connection timeout
modparam("db_postgres", "db_connection_timeout", 10)

# Enable connection ping
modparam("db_postgres", "ping_interval", 30)
```

**Issue 3: SIP Registration Failures**

*Symptoms:*
- Users cannot register
- "401 Unauthorized" responses
- Authentication challenges not working

*Diagnosis:*
```bash
# Check authentication configuration
kamctl db show subscriber
kamctl ul show

# Test manual registration
<NAME_EMAIL> testpass

# Monitor SIP traffic
ngrep -d any -qt -W byline "REGISTER" port 5060
```

*Solutions:*
```bash
# Verify database schema
kamctl db show version

# Reset user password
<NAME_EMAIL> newpassword

# Check authentication module configuration
grep -A5 -B5 "auth_db" /etc/kamailio/kamailio.cfg
```

### 6.2 Logging and Debugging Techniques

**Log Configuration:**
```bash
# Enable detailed logging
debug=3
log_stderror=no
log_facility=LOG_LOCAL0

# Custom log levels per module
modparam("debugger", "mod_level", "core=3")
modparam("debugger", "mod_level", "tm=3")
modparam("debugger", "mod_level", "auth=3")

# Log to custom file
log_name="kamailio"
```

**SIP Message Tracing:**
```bash
# Enable SIP message logging
loadmodule "siptrace.so"
modparam("siptrace", "duplicate_uri", "sip:127.0.0.1:9060")
modparam("siptrace", "trace_on", 1)

# Trace specific calls
route[TRACE] {
    if (is_method("INVITE")) {
        sip_trace();
    }
}
```

**Real-time Debugging:**
```bash
# Monitor logs in real-time
tail -f /var/log/syslog | grep kamailio

# Use kamctl for live debugging
kamctl monitor

# Check statistics
kamctl stats
kamctl stats core:
kamctl stats tm:
```

### 6.3 Performance Monitoring

**System Metrics:**
```bash
# Monitor CPU and memory usage
top -p $(pgrep kamailio | tr '\n' ',' | sed 's/,$//')

# Check network connections
ss -tuln | grep :5060
netstat -an | grep :5060

# Monitor disk I/O
iotop -p $(pgrep kamailio | tr '\n' ',' | sed 's/,$//')
```

**Kamailio Statistics:**
```bash
# Core statistics
kamctl stats core:rcv_requests
kamctl stats core:rcv_replies
kamctl stats core:fwd_requests
kamctl stats core:fwd_replies

# Transaction module statistics
kamctl stats tm:received_replies
kamctl stats tm:relayed_replies
kamctl stats tm:local_replies

# User location statistics
kamctl stats usrloc:registered_users
kamctl stats usrloc:location-users
kamctl stats usrloc:location-contacts
```

**Database Performance:**
```bash
# PostgreSQL query monitoring
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
WHERE query LIKE '%kamailio%'
ORDER BY total_time DESC;

# Connection monitoring
SELECT * FROM pg_stat_activity WHERE datname = 'kamailio';

# Table statistics
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
FROM pg_stat_user_tables
WHERE schemaname = 'public';
```

### 6.4 Backup and Recovery Procedures

**Configuration Backup:**
```bash
#!/bin/bash
# Kamailio configuration backup script

BACKUP_DIR="/backup/kamailio"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="kamailio_config_$DATE.tar.gz"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup configuration files
tar -czf $BACKUP_DIR/$BACKUP_FILE \
    /etc/kamailio/ \
    /etc/default/kamailio \
    /lib/systemd/system/kamailio.service

# Backup database schema
pg_dump -h localhost -U kamailio -s kamailio > $BACKUP_DIR/kamailio_schema_$DATE.sql

# Backup database data
pg_dump -h localhost -U kamailio -a kamailio > $BACKUP_DIR/kamailio_data_$DATE.sql

echo "Backup completed: $BACKUP_DIR/$BACKUP_FILE"
```

**Database Backup:**
```bash
# Full database backup
pg_dump -h localhost -U kamailio kamailio > kamailio_full_backup.sql

# Compressed backup
pg_dump -h localhost -U kamailio kamailio | gzip > kamailio_backup.sql.gz

# Backup specific tables
pg_dump -h localhost -U kamailio -t subscriber -t location kamailio > user_data_backup.sql
```

**Recovery Procedures:**
```bash
# Restore configuration
tar -xzf kamailio_config_backup.tar.gz -C /

# Restore database
dropdb kamailio
createdb kamailio
psql -h localhost -U kamailio kamailio < kamailio_full_backup.sql

# Restart services
systemctl restart postgresql
systemctl restart kamailio
```

---

## 7. Best Practices

### 7.1 Security Recommendations

**Network Security:**
```bash
# Implement IP-based access control
loadmodule "permissions.so"
modparam("permissions", "db_url", DBURL)

# Configure trusted networks
if (!allow_source_address()) {
    sl_send_reply("403", "Forbidden");
    exit;
}

# Rate limiting
loadmodule "pike.so"
modparam("pike", "sampling_time_unit", 2)
modparam("pike", "reqs_density_per_unit", 16)

if (!pike_check_req()) {
    sl_send_reply("503", "Service Unavailable");
    exit;
}
```

**Authentication Security:**
```bash
# Strong password policies
modparam("auth_db", "calculate_ha1", yes)
modparam("auth_db", "password_column", "password")

# Implement nonce expiration
modparam("auth", "nonce_expire", 300)
modparam("auth", "protect_contacts", 1)

# Use secure authentication realms
if (!auth_check("$fd", "subscriber", "1")) {
    auth_challenge("$fd", "0");
    exit;
}
```

**TLS/Encryption:**
```bash
# Force TLS for sensitive operations
if (is_method("REGISTER") && $pr != "tls") {
    sl_send_reply("403", "TLS Required");
    exit;
}

# Configure strong TLS settings
modparam("tls", "tls_method", "TLSv1.2")
modparam("tls", "cipher_list", "HIGH:!aNULL:!MD5")
modparam("tls", "verify_certificate", 1)
```

### 7.2 Scalability Considerations

**Horizontal Scaling:**
```bash
# Load balancer configuration
loadmodule "dispatcher.so"
modparam("dispatcher", "db_url", DBURL)
modparam("dispatcher", "flags", 2)

# Distribute load across multiple Kamailio instances
route[SCALE] {
    if (!ds_select_dst("1", "4")) {
        sl_send_reply("503", "Service Unavailable");
        exit;
    }
    route(RELAY);
}
```

**Database Scaling:**
```bash
# Read/write splitting
#!define DBURL_READ "postgres://kamailio_ro:<EMAIL>/kamailio"
#!define DBURL_WRITE "postgres://kamailio_rw:<EMAIL>/kamailio"

# Connection pooling
modparam("db_postgres", "max_db_connections", 50)
modparam("db_postgres", "connection_lifetime", 3600)
```

**Memory Optimization:**
```bash
# Optimize for high load
children=32
shm_mem=512
pkg_mem=32

# Enable memory debugging in development
memdbg=5
memlog=5
```

### 7.3 Maintenance Schedules

**Daily Tasks:**
```bash
#!/bin/bash
# Daily maintenance script

# Check service status
systemctl is-active kamailio postgresql

# Monitor log files for errors
grep -i error /var/log/syslog | grep kamailio | tail -20

# Check disk space
df -h /var/log /var/lib/postgresql

# Backup configuration
/usr/local/bin/kamailio_backup.sh
```

**Weekly Tasks:**
```bash
#!/bin/bash
# Weekly maintenance script

# Rotate logs
logrotate -f /etc/logrotate.d/kamailio

# Update statistics
kamctl stats > /var/log/kamailio/weekly_stats_$(date +%Y%m%d).log

# Check database performance
psql -U kamailio -c "ANALYZE;" kamailio

# Vacuum database
psql -U kamailio -c "VACUUM ANALYZE;" kamailio
```

**Monthly Tasks:**
```bash
#!/bin/bash
# Monthly maintenance script

# Full database backup
pg_dump kamailio > /backup/kamailio_monthly_$(date +%Y%m).sql

# Security audit
grep -i "auth\|fail\|error" /var/log/syslog | grep kamailio > /var/log/kamailio/security_audit_$(date +%Y%m).log

# Performance review
kamctl stats > /var/log/kamailio/performance_review_$(date +%Y%m).log

# Update system packages
apt update && apt list --upgradable | grep kamailio
```

**Configuration Management:**
```bash
# Version control for configurations
cd /etc/kamailio
git init
git add .
git commit -m "Initial Kamailio configuration"

# Before making changes
git add -A
git commit -m "Backup before changes on $(date)"

# After testing changes
git add -A
git commit -m "Applied configuration changes - $(description)"
```

---

## Conclusion

This comprehensive Kamailio SIP server documentation provides a complete guide for deploying, configuring, and maintaining a production-ready SIP infrastructure. The guide covers everything from basic setup to advanced features, practical examples, and best practices for security and scalability.

**Key Takeaways:**
- Kamailio is a powerful, flexible SIP server suitable for various telecommunications scenarios
- Proper configuration requires understanding of SIP protocols and routing logic
- Database integration is crucial for scalable deployments
- Security measures must be implemented from the beginning
- Regular maintenance and monitoring ensure optimal performance
- Documentation and version control are essential for production environments

**Next Steps:**
1. Review your specific requirements against the examples provided
2. Implement a test environment using the basic configuration scenarios
3. Gradually add advanced features as needed
4. Establish monitoring and maintenance procedures
5. Plan for scalability and high availability

For additional support and community resources, visit:
- **Kamailio Website**: https://www.kamailio.org/
- **Documentation**: https://www.kamailio.org/docs/
- **Community Forum**: https://lists.kamailio.org/
- **GitHub Repository**: https://github.com/kamailio/kamailio
